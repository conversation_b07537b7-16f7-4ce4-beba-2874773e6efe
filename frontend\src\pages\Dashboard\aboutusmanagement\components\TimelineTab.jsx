import React, { useState } from 'react';
import { Plus, Trash2, Edit, Save, X } from 'lucide-react';

const TimelineTab = ({ data, onChange, language }) => {
  const [editingIndex, setEditingIndex] = useState(null);
  const [editingData, setEditingData] = useState({});

  const addTimelineItem = () => {
    const currentTimeline = data.timeline || [];
    const newItem = {
      year: new Date().getFullYear().toString(),
      title: { [language]: '' },
      description: { [language]: '' },
      milestones: { [language]: [] }
    };
    
    onChange('timeline', [...currentTimeline, newItem]);
  };

  const removeTimelineItem = (index) => {
    const currentTimeline = data.timeline || [];
    const newTimeline = currentTimeline.filter((_, i) => i !== index);
    onChange('timeline', newTimeline);
  };

  const startEditing = (index) => {
    setEditingIndex(index);
    setEditingData({ ...data.timeline[index] });
  };

  const saveEditing = () => {
    const currentTimeline = [...(data.timeline || [])];
    currentTimeline[editingIndex] = editingData;
    onChange('timeline', currentTimeline);
    setEditingIndex(null);
    setEditingData({});
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingData({});
  };

  const handleEditingChange = (field, value) => {
    if (field === 'year') {
      setEditingData({ ...editingData, year: value });
    } else {
      setEditingData({
        ...editingData,
        [field]: {
          ...editingData[field],
          [language]: value
        }
      });
    }
  };

  const addMilestone = () => {
    const currentMilestones = editingData.milestones?.[language] || [];
    setEditingData({
      ...editingData,
      milestones: {
        ...editingData.milestones,
        [language]: [...currentMilestones, '']
      }
    });
  };

  const updateMilestone = (index, value) => {
    const currentMilestones = editingData.milestones?.[language] || [];
    const newMilestones = [...currentMilestones];
    newMilestones[index] = value;
    
    setEditingData({
      ...editingData,
      milestones: {
        ...editingData.milestones,
        [language]: newMilestones
      }
    });
  };

  const removeMilestone = (index) => {
    const currentMilestones = editingData.milestones?.[language] || [];
    const newMilestones = currentMilestones.filter((_, i) => i !== index);
    
    setEditingData({
      ...editingData,
      milestones: {
        ...editingData.milestones,
        [language]: newMilestones
      }
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          📅 Timeline Management
        </h2>
        <p className="text-gray-600">
          Manage the company timeline and milestones
        </p>
      </div>

      {/* Add New Timeline Item */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">
            Timeline Items ({language.toUpperCase()})
          </h3>
          <button
            onClick={addTimelineItem}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus size={16} />
            Add Timeline Item
          </button>
        </div>

        {/* Timeline Items List */}
        <div className="space-y-6">
          {(data.timeline || []).map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              {editingIndex === index ? (
                /* Editing Mode */
                <div className="space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-800">Edit Timeline Item</h4>
                    <div className="flex gap-2">
                      <button
                        onClick={saveEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save size={16} />
                        Save
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X size={16} />
                        Cancel
                      </button>
                    </div>
                  </div>

                  {/* Year */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
                    <input
                      type="text"
                      value={editingData.year || ''}
                      onChange={(e) => handleEditingChange('year', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="e.g., 2024"
                    />
                  </div>

                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input
                      type="text"
                      value={editingData.title?.[language] || ''}
                      onChange={(e) => handleEditingChange('title', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Timeline item title..."
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea
                      value={editingData.description?.[language] || ''}
                      onChange={(e) => handleEditingChange('description', e.target.value)}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="Timeline item description..."
                    />
                  </div>

                  {/* Milestones */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="block text-sm font-medium text-gray-700">Milestones</label>
                      <button
                        onClick={addMilestone}
                        className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm"
                      >
                        <Plus size={14} />
                        Add Milestone
                      </button>
                    </div>
                    
                    <div className="space-y-2">
                      {(editingData.milestones?.[language] || []).map((milestone, mIndex) => (
                        <div key={mIndex} className="flex items-center gap-2">
                          <input
                            type="text"
                            value={milestone}
                            onChange={(e) => updateMilestone(mIndex, e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                            placeholder={`Milestone ${mIndex + 1}...`}
                          />
                          <button
                            onClick={() => removeMilestone(mIndex)}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                /* Display Mode */
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-bold text-sm">
                        {item.year}
                      </div>
                      <h4 className="text-lg font-semibold text-gray-800">
                        {item.title?.[language] || 'Untitled'}
                      </h4>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => startEditing(index)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => removeTimelineItem(index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-3">
                    {item.description?.[language] || 'No description'}
                  </p>
                  
                  {item.milestones?.[language] && item.milestones[language].length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Milestones:</h5>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                        {item.milestones[language].map((milestone, mIndex) => (
                          <li key={mIndex}>{milestone}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}

          {(!data.timeline || data.timeline.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <p className="text-lg mb-2">No timeline items yet</p>
              <p className="text-sm">Click "Add Timeline Item" to get started</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimelineTab;
