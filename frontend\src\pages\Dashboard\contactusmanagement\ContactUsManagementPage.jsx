import React, { useState, useEffect } from 'react';
import { Save, AlertCircle, CheckCircle } from 'lucide-react';
import ContactInfoTab from './components/ContactInfoTab';
import ContactFormTab from './components/ContactFormTab';

const ContactUsManagementPage = () => {
  const [activeTab, setActiveTab] = useState('info');
  const [contactUsData, setContactUsData] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const API_URL = 'http://localhost:5000/api/contact-us';

  useEffect(() => {
    fetchContactUsData();
  }, []);

  const fetchContactUsData = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch(`${API_URL}/admin`);
      
      if (!response.ok) {
        if (response.status === 404) {
          // No contact us data exists, create default structure
          setContactUsData(getDefaultData());
        } else {
          throw new Error('Failed to fetch contact us data.');
        }
      } else {
        const data = await response.json();
        setContactUsData(data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getDefaultData = () => ({
    contactInfo: {
      address: { fr: '', ar: '' },
      city: { fr: '', ar: '' },
      country: { fr: '', ar: '' },
      postalCode: '',
      phone: '',
      secondaryPhone: '',
      email: '',
      supportEmail: '',
      businessHours: { fr: '', ar: '' }
    },
    contactForm: {
      title: { fr: '', ar: '' },
      subtitle: { fr: '', ar: '' },
      labels: {
        name: { fr: '', ar: '' },
        email: { fr: '', ar: '' },
        subject: { fr: '', ar: '' },
        message: { fr: '', ar: '' }
      },
      submitButton: { fr: '', ar: '' },
      settings: {
        recipientEmail: '',
        autoReplyEmail: '',
        enableCaptcha: false
      }
    }
  });

  const saveContactUsData = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccessMessage('');

      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(contactUsData)
      });

      if (!response.ok) {
        throw new Error('Failed to save contact us data.');
      }

      const updatedData = await response.json();
      setContactUsData(updatedData);
      setSuccessMessage('Contact Us content saved successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      setError('Error saving contact us data: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleDataChange = (section, newData) => {
    setContactUsData(prev => ({
      ...prev,
      [section]: newData
    }));
  };

  const tabs = [
    { id: 'info', label: 'Contact Information', icon: '📞' },
    { id: 'form', label: 'Contact Form', icon: '📝' }
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Contact Us Management</h1>
          <p className="text-gray-600">Manage contact information, form settings, and content with multilingual support</p>
        </div>
        
        <div className="mt-4 lg:mt-0">
          {/* Save Button */}
          <button
            onClick={saveContactUsData}
            disabled={saving}
            className="flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save size={20} />
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3">
          <AlertCircle size={20} className="text-red-600 flex-shrink-0" />
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3">
          <CheckCircle size={20} className="text-green-600 flex-shrink-0" />
          <p className="text-green-800">{successMessage}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-gray-50 rounded-xl p-6">
        {activeTab === 'info' && (
          <ContactInfoTab
            data={contactUsData}
            onChange={handleDataChange}
          />
        )}

        {activeTab === 'form' && (
          <ContactFormTab
            data={contactUsData}
            onChange={handleDataChange}
          />
        )}
      </div>
    </div>
  );
};

export default ContactUsManagementPage;
