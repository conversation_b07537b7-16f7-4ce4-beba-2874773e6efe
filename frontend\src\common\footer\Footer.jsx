import React, { useState, useEffect, useRef } from 'react';
import { FaFacebookF, FaTwitter, FaInstagram, FaLinkedinIn, FaCcVisa, FaCcMastercard, FaCcPaypal, FaApplePay, FaGooglePay, FaYoutube, FaTiktok, FaPinterest } from 'react-icons/fa';
import { MdSecurity, MdLocalShipping, MdHighQuality, MdOutlineLanguage, MdEmail } from 'react-icons/md';
import { IoIosArrowDown, IoIosArrowUp } from 'react-icons/io';
import { FiGift, FiShield, FiStar, FiTruck, FiRefreshCw, FiHeadphones } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';

// Removed the duplicate import: import FooterLink from './FooterLink';

const AdvancedFeatureFooter = () => {
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('Français');
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [footerData, setFooterData] = useState(null);
  const [loading, setLoading] = useState(true);
  const languageMenuRef = useRef(null);

  // Initialize language from localStorage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (savedLanguage) {
      const language = languages.find(lang => lang.code === savedLanguage);
      if (language) {
        setSelectedLanguage(language.name);
      }
    } else {
      // Set default language to French
      localStorage.setItem('selectedLanguage', 'fr');
    }
  }, []);
  
  // Fetch footer data from API
  useEffect(() => {
    const fetchFooterData = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:5000/api/footer');
        if (response.ok) {
          const data = await response.json();
          setFooterData(data);
        }
      } catch (error) {
        console.error('Error fetching footer data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFooterData();
  }, []);

  // Helper function to get social media icons
  const getSocialIcon = (platform) => {
    const icons = {
      facebook: <FaFacebookF />,
      twitter: <FaTwitter />,
      instagram: <FaInstagram />,
      linkedin: <FaLinkedinIn />,
      youtube: <FaYoutube />,
      tiktok: <FaTiktok />,
      pinterest: <FaPinterest />
    };
    return icons[platform] || <FaFacebookF />;
  };

  // Helper function to get social media colors
  const getSocialColor = (platform) => {
    const colors = {
      facebook: "hover:text-blue-500",
      twitter: "hover:text-sky-400",
      instagram: "hover:text-pink-500",
      linkedin: "hover:text-blue-400",
      youtube: "hover:text-red-500",
      tiktok: "hover:text-gray-300",
      pinterest: "hover:text-red-400"
    };
    return colors[platform] || "hover:text-blue-500";
  };

  // Helper function to get feature icons
  const getFeatureIcon = (iconName) => {
    const icons = {
      truck: <FiTruck className="text-4xl text-purple-400" />,
      shield: <FiShield className="text-4xl text-purple-400" />,
      refresh: <FiRefreshCw className="text-4xl text-purple-400" />,
      headphones: <FiHeadphones className="text-4xl text-purple-400" />,
      gift: <FiGift className="text-4xl text-purple-400" />,
      star: <FiStar className="text-4xl text-purple-400" />
    };
    return icons[iconName] || <FiGift className="text-4xl text-purple-400" />;
  };

  // Languages data with flags - mapped to our About Us language codes
  const languages = [
    { name: 'Français', code: 'fr', flag: '🇫🇷' },
    { name: 'العربية', code: 'ar', flag: '🇸🇦' },
  ];

  const features = [
    {
      icon: <FiGift className="text-4xl text-purple-400" />,
      title: 'Free & Fast Shipping',
      description: 'Enjoy complimentary, expedited shipping on all orders over $50.',
      extendedDesc: 'All orders ship within 24 hours with tracking included. International shipping available to 100+ countries.'
    },
    {
      icon: <FiShield className="text-4xl text-purple-400" />,
      title: 'Secure Payments',
      description: 'Your payments are processed securely with top-tier encryption.',
      extendedDesc: 'We use military-grade 256-bit SSL encryption and are PCI DSS compliant. Your financial data is never stored.'
    },
    {
      icon: <FiStar className="text-4xl text-purple-400" />,
      title: 'Premium Quality',
      description: 'We source the finest materials to guarantee product excellence.',
      extendedDesc: 'Every product undergoes a 7-step quality inspection process and comes with a 2-year satisfaction guarantee.'
    }
  ];

  // Validate email function
  const validateEmail = (email) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  // Handle newsletter subscription
  const handleSubscribe = (e) => {
    e.preventDefault();
    
    if (!email) {
      setEmailError('Please enter your email');
      return;
    }
    
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }
    
    // Simulate API call
    setEmailError('');
    setIsSubscribed(true);
    setTimeout(() => {
      setEmail('');
      setIsSubscribed(false);
    }, 3000);
  };

  // Close language menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target)) {
        setShowLanguageMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <footer className="bg-gradient-to-b from-gray-900 to-black text-white pt-20 pb-8 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
        <div className="absolute top-10 left-1/4 w-64 h-64 rounded-full bg-purple-600 blur-[100px]"></div>
        <div className="absolute bottom-20 right-1/4 w-48 h-48 rounded-full bg-indigo-600 blur-[80px]"></div>
      </div>
      
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        {/* Top Section: Why Shop With Us */}
        {footerData?.whyShopWithUs?.isActive !== false && (
          <div className="mb-20 pb-20 border-b border-gray-800">
            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-3xl md:text-4xl font-bold text-center mb-16 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-400 to-purple-600"
            >
              {footerData?.whyShopWithUs?.title || 'Why Shop With Us?'}
            </motion.h3>

            <div className="flex flex-wrap justify-center items-stretch gap-8">
              {(footerData?.whyShopWithUs?.features?.filter(feature => feature.isActive) || features).map((feature, index) => (
                <FeatureCard
                  key={index}
                  icon={footerData?.whyShopWithUs?.features ? getFeatureIcon(feature.icon) : feature.icon}
                  title={feature.title}
                  description={feature.description}
                  extendedDesc={feature.extendedDesc || feature.description}
                />
              ))}
            </div>
          </div>
        )}

        {/* Mid Section: Footer columns */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-10 mb-10">
          {/* Brand & Socials */}
          <div className="mb-8 md:mb-0">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                {footerData?.brandInfo?.name || 'StyleHub'}
              </h2>
              <p className="text-gray-400 mb-6">
                {footerData?.brandInfo?.description || 'Curated products for the modern world. Built on quality, trust, and exceptional customer experiences.'}
              </p>
              <div className="flex space-x-4">
                {footerData?.socialMedia?.filter(social => social.isActive).map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.url}
                    aria-label={social.platform}
                    className="text-xl text-gray-400 transition-colors p-2 rounded-full bg-gray-800 hover:bg-gray-700"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className={getSocialColor(social.platform)}>{getSocialIcon(social.platform)}</div>
                  </motion.a>
                )) || [
                  { icon: <FaFacebookF />, label: "Facebook", color: "hover:text-blue-500", url: "https://facebook.com" },
                  { icon: <FaTwitter />, label: "Twitter", color: "hover:text-sky-400", url: "https://twitter.com" },
                  { icon: <FaInstagram />, label: "Instagram", color: "hover:text-pink-500", url: "https://instagram.com" },
                  { icon: <FaLinkedinIn />, label: "LinkedIn", color: "hover:text-blue-400", url: "https://linkedin.com" },
                ].map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.url}
                    aria-label={social.label}
                    className="text-xl text-gray-400 transition-colors p-2 rounded-full bg-gray-800 hover:bg-gray-700"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className={social.color}>{social.icon}</div>
                  </motion.a>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Dynamic Footer Sections */}
          {footerData?.sections?.filter(section => section.isActive)
            .sort((a, b) => a.order - b.order)
            .map((section, sectionIndex) => (
            <motion.div
              key={sectionIndex}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.2 + (sectionIndex * 0.1) }}
            >
              <h3 className="text-lg font-semibold mb-4 text-gray-200 flex items-center">
                <span className={`w-3 h-3 ${sectionIndex % 2 === 0 ? 'bg-purple-500' : 'bg-indigo-500'} rounded-full mr-2`}></span>
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links?.filter(link => link.isActive).map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <FooterLink href={link.url} text={link.text} />
                  </li>
                ))}
              </ul>
            </motion.div>
          )) || [
            // Fallback sections if no data
            <motion.div
              key="customer-service"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <h3 className="text-lg font-semibold mb-4 text-gray-200 flex items-center">
                <span className="w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
                Customer Service
              </h3>
              <ul className="space-y-3">
                {['FAQ', 'Contact Us', 'Shipping & Returns', 'Order Tracking', 'Size Guide'].map((item, index) => (
                  <li key={index}>
                    <FooterLink href={`/${item.toLowerCase().replace(' ', '-')}`} text={item} />
                  </li>
                ))}
              </ul>
            </motion.div>,
            <motion.div
              key="company"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.3 }}
            >
              <h3 className="text-lg font-semibold mb-4 text-gray-200 flex items-center">
                <span className="w-3 h-3 bg-indigo-500 rounded-full mr-2"></span>
                Company
              </h3>
              <ul className="space-y-3">
                {['About Us', 'Careers', 'Press', 'Affiliate Program', 'Sustainability'].map((item, index) => (
                  <li key={index}>
                    <FooterLink href={`/${item.toLowerCase().replace(' ', '-')}`} text={item} />
                  </li>
                ))}
              </ul>
            </motion.div>
          ]}

          {/* Popular Categories */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            <h3 className="text-lg font-semibold mb-4 text-gray-200 flex items-center">
              <span className="w-3 h-3 bg-purple-500 rounded-full mr-2"></span>
              Shop
            </h3>
            <ul className="space-y-3">
              {['New Arrivals', 'Best Sellers', 'Summer Collection', 'Winter Essentials', 'Accessories'].map((item, index) => (
                <li key={index}>
                  <FooterLink href={`/category/${item.toLowerCase().replace(' ', '-')}`} text={item} />
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Newsletter */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.4, delay: 0.5 }}
          >
            <h3 className="text-lg font-semibold mb-4 text-gray-200 flex items-center">
              <span className="w-3 h-3 bg-indigo-500 rounded-full mr-2"></span>
              Join Our Newsletter
            </h3>
            <p className="text-gray-400 mb-4">
              Subscribe to receive updates, exclusive deals, and style inspiration.
            </p>
            
            <form onSubmit={handleSubscribe} className="mb-2">
              <div className="relative">
                <MdEmail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xl" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className="bg-gray-800 text-white pl-10 pr-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 w-full placeholder-gray-500"
                />
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-1 rounded-md font-medium text-sm"
                >
                  Join
                </motion.button>
              </div>
              
              {emailError && (
                <p className="text-red-400 text-sm mt-2 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {emailError}
                </p>
              )}
              
              {isSubscribed && (
                <motion.div 
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-green-400 text-sm mt-2 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  Thank you for subscribing!
                </motion.div>
              )}
            </form>
            
            <div className="mt-6">
              <h4 className="text-gray-300 text-sm font-medium mb-2">We Accept:</h4>
              <div className="flex flex-wrap gap-3 text-2xl text-gray-500">
                <FaCcVisa className="hover:text-blue-600 transition-colors" />
                <FaCcMastercard className="hover:text-red-600 transition-colors" />
                <FaCcPaypal className="hover:text-blue-400 transition-colors" />
                <FaApplePay className="hover:text-gray-300 transition-colors" />
                <FaGooglePay className="hover:text-green-500 transition-colors" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section: Legal & Internationalization */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-500 text-sm mb-4 md:mb-0">
              © {footerData?.copyright?.year || new Date().getFullYear()} {footerData?.copyright?.text || 'StyleHub. All Rights Reserved.'}
              <div className="mt-2 flex flex-wrap gap-4 text-xs">
                {footerData?.legalLinks?.filter(link => link.isActive).map((link, index) => (
                  <a
                    key={index}
                    href={link.url}
                    className="text-gray-500 hover:text-gray-300 transition-colors"
                  >
                    {link.text}
                  </a>
                )) || [
                  <a key="privacy" href="/privacy" className="text-gray-500 hover:text-gray-300 transition-colors">Privacy Policy</a>,
                  <a key="terms" href="/terms" className="text-gray-500 hover:text-gray-300 transition-colors">Terms of Service</a>,
                  <a key="cookies" href="/cookies" className="text-gray-500 hover:text-gray-300 transition-colors">Cookie Policy</a>
                ]}
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="relative" ref={languageMenuRef}>
                <motion.button 
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)} 
                  className="flex items-center text-gray-400 hover:text-white transition-colors px-3 py-2 rounded-lg bg-gray-800"
                >
                  <MdOutlineLanguage className="mr-2 text-lg"/>
                  <span className="mr-2">{selectedLanguage}</span>
                  {showLanguageMenu ? <IoIosArrowUp /> : <IoIosArrowDown />}
                </motion.button>
                
                <AnimatePresence>
                  {showLanguageMenu && (
                    <motion.div 
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute bottom-full mb-2 right-0 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50"
                    >
                      {languages.map((lang) => (
                        <button
                          key={lang.code}
                          onClick={() => {
                            setSelectedLanguage(lang.name);
                            setShowLanguageMenu(false);
                            // Save language to localStorage
                            localStorage.setItem('selectedLanguage', lang.code);
                            // Trigger storage event for other components to listen
                            window.dispatchEvent(new Event('storage'));
                          }}
                          className={`w-full text-left px-4 py-3 text-sm hover:bg-gray-700 transition-colors flex items-center ${
                            selectedLanguage === lang.name ? 'bg-purple-900/50 text-purple-300' : 'text-gray-300'
                          }`}
                        >
                          <span className="text-xl mr-3">{lang.flag}</span>
                          <span>{lang.name}</span>
                          {selectedLanguage === lang.name && (
                            <span className="ml-auto text-green-400">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </span>
                          )}
                        </button>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Back to Top Button */}
      <motion.button 
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-6 right-6 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-purple-600 transition-colors z-50 border border-gray-700"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label="Back to top"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
        </svg>
      </motion.button>
    </footer>
  );
};

// FeatureCard Component with enhanced design
const FeatureCard = ({ icon, title, description, extendedDesc }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div 
      className="w-full max-w-md md:w-80"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.5 }}
    >
      <div 
        className="relative h-full rounded-xl overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Card background with glass effect */}
        <div 
          className="h-full p-6 flex flex-col items-center text-center transition-all duration-500"
          style={{
            background: 'rgba(30, 30, 40, 0.6)',
            backdropFilter: 'blur(12px)',
            border: '1px solid rgba(255, 255, 255, 0.08)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
          }}
        >
          {/* Icon container with animated gradient */}
          <motion.div 
            className="p-4 rounded-2xl mb-5 relative"
            animate={{
              background: isHovered 
                ? ['linear-gradient(45deg, #7e22ce, #4338ca)'] 
                : ['linear-gradient(45deg, rgba(126, 34, 206, 0.1), rgba(67, 56, 202, 0.1))']
            }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative z-10">
              {icon}
            </div>
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-indigo-600/20 rounded-2xl"></div>
          </motion.div>
          
          <h4 className="text-xl font-bold text-white mb-2">{title}</h4>
          <p className="text-gray-300 mb-4">{description}</p>
          
          {/* Extended description on hover */}
          <motion.div 
            className="text-sm text-gray-400 mt-auto"
            initial={{ opacity: 0, height: 0 }}
            animate={{ 
              opacity: isHovered ? 1 : 0,
              height: isHovered ? 'auto' : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {extendedDesc}
          </motion.div>
          
          {/* Learn More button */}
          <motion.button
            className="mt-4 text-sm font-medium text-purple-400 flex items-center group"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ delay: isHovered ? 0.2 : 0 }}
          >
            Learn more
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </motion.button>
        </div>
        
        {/* Animated glow effect */}
        <motion.div 
          className="absolute inset-0 rounded-xl opacity-0 pointer-events-none"
          animate={{ 
            opacity: isHovered ? 0.3 : 0,
            boxShadow: isHovered ? '0 0 40px rgba(139, 92, 246, 0.6)' : '0 0 0px rgba(139, 92, 246, 0)'
          }}
          transition={{ duration: 0.4 }}
        ></motion.div>
      </div>
    </motion.div>
  );
};

// FooterLink Component with enhanced animation
const FooterLink = ({ href, text }) => {
  return (
    <motion.a
      href={href}
      className="relative text-gray-400 transition-colors duration-300 hover:text-white inline-block py-1"
      whileHover={{ x: 5 }}
      whileTap={{ scale: 0.95 }}
    >
      <span className="relative z-10">{text}</span>
      <motion.div 
        className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full"
        initial={{ width: 0 }}
        whileHover={{ width: '100%' }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
    </motion.a>
  );
};

export default AdvancedFeatureFooter;