import React, { useState, useRef } from 'react';
import { Plus, Trash2, Upload, X } from 'lucide-react';

const CoreValuesTab = ({ data, onChange }) => {
  const [activePanel, setActivePanel] = useState('vision');
  const fileInputRefs = useRef({});

  const handleSectionChange = (section, field, value, lang = 'fr') => {
    onChange('coreValues', {
      ...data.coreValues,
      [section]: {
        ...data.coreValues[section],
        [field]: {
          ...data.coreValues[section]?.[field],
          [lang]: value
        }
      }
    });
  };

  const handlePointChange = (section, index, value, lang = 'fr') => {
    const currentPoints = data.coreValues[section]?.points?.[lang] || [];
    const newPoints = [...currentPoints];
    newPoints[index] = value;

    onChange('coreValues', {
      ...data.coreValues,
      [section]: {
        ...data.coreValues[section],
        points: {
          ...data.coreValues[section]?.points,
          [lang]: newPoints
        }
      }
    });
  };

  const addPoint = (section) => {
    const currentPointsFr = data.coreValues[section]?.points?.fr || [];
    const currentPointsAr = data.coreValues[section]?.points?.ar || [];

    onChange('coreValues', {
      ...data.coreValues,
      [section]: {
        ...data.coreValues[section],
        points: {
          fr: [...currentPointsFr, ''],
          ar: [...currentPointsAr, '']
        }
      }
    });
  };

  const removePoint = (section, index) => {
    const currentPointsFr = data.coreValues[section]?.points?.fr || [];
    const currentPointsAr = data.coreValues[section]?.points?.ar || [];

    onChange('coreValues', {
      ...data.coreValues,
      [section]: {
        ...data.coreValues[section],
        points: {
          fr: currentPointsFr.filter((_, i) => i !== index),
          ar: currentPointsAr.filter((_, i) => i !== index)
        }
      }
    });
  };

  const handleImageUpload = (section, e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        onChange('coreValues', {
          ...data.coreValues,
          [section]: {
            ...data.coreValues[section],
            image: event.target.result
          }
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = (section) => {
    onChange('coreValues', {
      ...data.coreValues,
      [section]: {
        ...data.coreValues[section],
        image: ''
      }
    });
  };

  const sections = ['vision', 'mission', 'story'];

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-xl border border-blue-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          💎 Core Values Management
        </h2>
        <p className="text-gray-600">
          Configure the three main sections: Vision, Mission, and Story
        </p>
      </div>

      {/* Section Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-wrap gap-2 mb-6">
          {sections.map((section) => (
            <button
              key={section}
              onClick={() => setActivePanel(section)}
              className={`px-6 py-3 rounded-lg font-semibold capitalize transition-all ${
                activePanel === section
                  ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {section}
            </button>
          ))}
        </div>

        {/* Active Section Content */}
        <div className="space-y-6">
          {/* Section Header */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 capitalize">
              {activePanel} Section
            </h3>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Title</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.coreValues?.[activePanel]?.title?.fr || ''}
                  onChange={(e) => handleSectionChange(activePanel, 'title', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={`Enter ${activePanel} title in French...`}
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.coreValues?.[activePanel]?.title?.ar || ''}
                  onChange={(e) => handleSectionChange(activePanel, 'title', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder={`أدخل عنوان ${activePanel} بالعربية...`}
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Description</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.coreValues?.[activePanel]?.description?.fr || ''}
                  onChange={(e) => handleSectionChange(activePanel, 'description', e.target.value, 'fr')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={`Enter ${activePanel} description in French...`}
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.coreValues?.[activePanel]?.description?.ar || ''}
                  onChange={(e) => handleSectionChange(activePanel, 'description', e.target.value, 'ar')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder={`أدخل وصف ${activePanel} بالعربية...`}
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Points */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Key Points
              </label>
              <button
                onClick={() => addPoint(activePanel)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus size={16} />
                Add Point
              </button>
            </div>
            
            <div className="space-y-4">
              {(data.coreValues?.[activePanel]?.points?.fr || []).map((_, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-700">Point {index + 1}</h4>
                    <button
                      onClick={() => removePoint(activePanel, index)}
                      className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                      <input
                        type="text"
                        value={data.coreValues?.[activePanel]?.points?.fr?.[index] || ''}
                        onChange={(e) => handlePointChange(activePanel, index, e.target.value, 'fr')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        placeholder={`Point ${index + 1} in French...`}
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                      <input
                        type="text"
                        value={data.coreValues?.[activePanel]?.points?.ar?.[index] || ''}
                        onChange={(e) => handlePointChange(activePanel, index, e.target.value, 'ar')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-right"
                        placeholder={`النقطة ${index + 1} بالعربية...`}
                        dir="rtl"
                      />
                    </div>
                  </div>
                </div>
              ))}

              {(!data.coreValues?.[activePanel]?.points?.fr || data.coreValues[activePanel].points.fr.length === 0) && (
                <p className="text-gray-500 text-center py-4">No points added yet. Click "Add Point" to get started.</p>
              )}
            </div>
          </div>

          {/* Section Image */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Section Image
            </label>
            
            {data.coreValues?.[activePanel]?.image ? (
              <div className="relative">
                <img 
                  src={data.coreValues[activePanel].image} 
                  alt={`${activePanel} section`} 
                  className="w-full h-48 object-cover rounded-lg border-2 border-gray-200"
                />
                <button
                  onClick={() => removeImage(activePanel)}
                  className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <div 
                onClick={() => fileInputRefs.current[activePanel]?.click()}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
              >
                <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-2">Click to upload {activePanel} image</p>
                <p className="text-sm text-gray-400">Recommended: 800x600px, JPG or PNG</p>
              </div>
            )}
            
            <input
              ref={(el) => fileInputRefs.current[activePanel] = el}
              type="file"
              accept="image/*"
              onChange={(e) => handleImageUpload(activePanel, e)}
              className="hidden"
            />
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Preview</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {data.coreValues?.[activePanel]?.title?.[language] || `${activePanel} Title`}
          </h3>
          <p className="text-gray-700 mb-6">
            {data.coreValues?.[activePanel]?.description?.[language] || `${activePanel} description...`}
          </p>
          <ul className="space-y-2">
            {(data.coreValues?.[activePanel]?.points?.[language] || []).map((point, index) => (
              <li key={index} className="flex items-start">
                <div className="bg-blue-100 rounded-full p-1 mr-3 mt-1">
                  <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700">{point}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CoreValuesTab;
