import mongoose from 'mongoose';

const contactUsSchema = new mongoose.Schema({
  contactInfo: {
    address: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    city: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    country: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    postalCode: { type: String, default: '' },
    phone: { type: String, default: '' },
    secondaryPhone: { type: String, default: '' },
    email: { type: String, default: '' },
    supportEmail: { type: String, default: '' },
    businessHours: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    }
  },
  contactForm: {
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    subtitle: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    labels: {
      name: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      email: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      subject: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      message: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      }
    },
    submitButton: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    settings: {
      recipientEmail: { type: String, default: '' },
      autoReplyEmail: { type: String, default: '' },
      enableCaptcha: { type: Boolean, default: false }
    }
  }
}, {
  timestamps: true
});

export default mongoose.model('ContactUs', contactUsSchema);
