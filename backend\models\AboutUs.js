import mongoose from 'mongoose';

const aboutUsSchema = new mongoose.Schema({
  heroSection: {
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    highlightedText: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    subtitle: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    buttonText: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    backgroundImage: { type: String, default: '' }
  },
  coreValues: {
    vision: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    },
    mission: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    },
    story: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    }
  },
  timeline: [{
    year: { type: String, required: true },
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    description: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    milestones: {
      fr: [{ type: String }],
      ar: [{ type: String }]
    }
  }],
  callToAction: {
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    subtitle: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    primaryButton: {
      text: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      link: { type: String, default: '' }
    },
    secondaryButton: {
      text: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      link: { type: String, default: '' }
    },
    backgroundImage: { type: String, default: '' }
  }
}, {
  timestamps: true
});

export default mongoose.model('AboutUs', aboutUsSchema);
